# BC Monitoring Service — Basic Test Cases

> **Purpose**: Essential smoke tests and core functionality validation for BC Monitoring Service
> 
> **Scope**: Critical scenarios for basic testing and initial validation
> 
> **Reference**: See `BC_Monitoring_Service_Test_Matrix.md` for comprehensive test coverage

---

## 1. Service Startup & Initialization

### ✅ Normal Cases
#### 1.1 Successful Service Startup
- **Input**: Valid environment variables, accessible S3 with ABI files, accessible DynamoDB, working WebSocket endpoint
- **Expected**: Service starts, logs "started bc monitoring", begins event monitoring

#### 1.2 Service Auto-Restart
- **Input**: WebSocket handshake error ("rpc.wsHandshakeError")
- **Expected**: Service automatically restarts monitoring, logs "restart bc monitoring"

### ⚠️ Edge Cases
#### 1.3 Empty ABI Bucket
- **Input**: S3 bucket exists but contains no ABI files
- **Expected**: Service starts but cannot detect any contract events

#### 1.4 Missing Block Height
- **Input**: Empty DynamoDB BlockHeight table
- **Expected**: Service starts monitoring from block 1

### ❌ Failure Cases
#### 1.5 Missing Environment Variables
- **Input**: Missing required env vars (WEBSOCKET_URI_HOST, WEBSOCKET_URI_PORT, etc.)
- **Expected**: Service exits with fatal error

#### 1.6 DynamoDB Connection Failure
- **Input**: Invalid DynamoDB endpoint or credentials
- **Expected**: Service exits with fatal error during initialization

---

## 2. ABI File Processing

### ✅ Normal Cases
#### 2.1 Valid ABI Download
- **Input**: S3 bucket with valid JSON ABI files in zone directories (e.g., "3000/Contract.json")
- **Expected**: ABI files parsed, contract addresses loaded, events available for monitoring

#### 2.2 Multiple Zone Processing
- **Input**: ABI files in multiple zone directories (3000, 3001, 3002)
- **Expected**: All zone ABI files processed successfully

### ⚠️ Edge Cases
#### 2.3 Non-JSON Files
- **Input**: S3 bucket containing .txt, .md files mixed with .json files
- **Expected**: Non-JSON files skipped, JSON files processed normally

### ❌ Failure Cases
#### 2.4 S3 Access Denied
- **Input**: S3 bucket with access denied or invalid credentials
- **Expected**: Service exits with fatal error "failed to list s3 CommonPrefixes objects"

#### 2.5 Invalid JSON Content
- **Input**: Malformed or corrupted JSON files in S3
- **Expected**: Service exits with fatal error "failed to unmarshal abi json"

#### 2.6 Missing ABI Field
- **Input**: Valid JSON file without required "abi" field
- **Expected**: Service exits with fatal error during ABI unmarshaling

---

## 3. Blockchain Event Monitoring

### ✅ Normal Cases
#### 3.1 Event Detection & Processing
- **Input**: New blockchain blocks with contract events matching loaded ABIs
- **Expected**: Events extracted, parsed, saved to DynamoDB with correct transaction hash and data

#### 3.2 Pending Transaction Processing
- **Input**: Pending transactions from last processed block height + 1
- **Expected**: Pending transactions processed, events saved

#### 3.3 Event Data Parsing
- **Input**: Contract events with indexed and non-indexed parameters
- **Expected**: Event data correctly parsed into IndexedValues and NonIndexedValues JSON

### ⚠️ Edge Cases
#### 3.4 Blocks with No Events
- **Input**: Blockchain blocks with no events matching loaded ABIs
- **Expected**: Block processed without errors, block height updated

#### 3.5 Events Without TraceId
- **Input**: Contract events missing traceId in non-indexed values
- **Expected**: Event processed normally, empty traceId logged

### ❌ Failure Cases
#### 3.6 WebSocket Connection Failure
- **Input**: WebSocket connection drops during monitoring
- **Expected**: Error logged, monitoring stops, service attempts restart

#### 3.7 Empty Transaction Hash
- **Input**: Blockchain events with empty transaction hash
- **Expected**: Event rejected, error logged "event transaction hash is zero"

#### 3.8 Block Number Zero
- **Input**: Blockchain events with BlockNumber = 0
- **Expected**: Block rejected, warning logged, monitoring restarts after interval

---

## 4. Data Persistence

### ✅ Normal Cases
#### 4.1 Event Storage
- **Input**: Valid parsed events with all required fields
- **Expected**: Events stored in DynamoDB Events table, success logged

#### 4.2 Block Height Update
- **Input**: Successfully processed block with events
- **Expected**: Block height updated in DynamoDB BlockHeight table

#### 4.3 Multiple Events Processing
- **Input**: Multiple events from same transaction/block
- **Expected**: All events stored individually with correct log indexes

### ⚠️ Edge Cases
#### 4.4 Pending vs Regular Transactions
- **Input**: Mix of pending and regular blockchain transactions
- **Expected**: Pending transactions save events only; regular transactions save events AND update block height

#### 4.5 Empty Events List
- **Input**: Transactions with empty Events array
- **Expected**: Transaction processed, block height updated, no events stored

### ❌ Failure Cases
#### 4.6 DynamoDB Write Failure
- **Input**: DynamoDB PutItem operations fail
- **Expected**: Event save returns false, error logged, monitoring stops and retries

#### 4.7 DynamoDB Read Failure
- **Input**: DynamoDB Query fails when retrieving block height
- **Expected**: Error logged "failed to get blockheight", monitoring stops

---

## 5. Configuration & Error Handling

### ✅ Normal Cases
#### 5.1 Environment Variable Loading
- **Input**: All required environment variables set with valid values
- **Expected**: Configuration values loaded and accessible

#### 5.2 Local vs Production Environment
- **Input**: ENV set to "local" or "prod"
- **Expected**: Appropriate S3 and DynamoDB endpoints configured

### ⚠️ Edge Cases
#### 5.3 Default Configuration Values
- **Input**: Missing optional configuration parameters
- **Expected**: Service uses default values, operates normally

### ❌ Failure Cases
#### 5.4 Invalid Configuration Values
- **Input**: SUBSCRIPTION_CHECK_INTERVAL set to non-numeric value
- **Expected**: Service fails with error "faild to convert checkInterval"

#### 5.5 Invalid Timestamp Settings
- **Input**: ALLOWABLE_BLOCK_TIMESTAMP_DIFF_SEC set to non-numeric value
- **Expected**: Service fails during event log DAO initialization

---

## 6. Error Recovery & Resilience

### ✅ Normal Cases
#### 6.1 WebSocket Retry Mechanism
- **Input**: WebSocket handshake errors ("rpc.wsHandshakeError")
- **Expected**: Service retries up to 5 times, reinitializes monitor on retry

### ⚠️ Edge Cases
#### 6.2 Non-Critical Error Handling
- **Input**: Events that cannot be parsed due to missing ABI definitions
- **Expected**: Specific events skipped, monitoring continues for other events

#### 6.3 Internal Retry Pattern
- **Input**: DynamoDB save failures, block number zero, empty transaction hash
- **Expected**: Service uses goto RETRY pattern, sleeps for checkInterval, restarts monitoring

### ❌ Failure Cases
#### 6.4 Fatal Error Propagation
- **Input**: Critical errors (DynamoDB connection failures, ABI parsing failures)
- **Expected**: Errors propagated to main function, service exits with l.Fatalln()

#### 6.5 Non-Retryable Errors
- **Input**: Errors other than "rpc.wsHandshakeError"
- **Expected**: Service logs warning "restarting bc monitoring", continues infinite retry loop

---

## Quick Test Execution Guide

### Smoke Test Sequence (Essential)
1. **Service Startup** (1.1) - Verify basic functionality
2. **ABI Processing** (2.1) - Ensure contract definitions load
3. **Event Detection** (3.1) - Confirm blockchain monitoring works
4. **Data Storage** (4.1, 4.2) - Validate persistence layer

### Critical Failure Tests
1. **Missing Dependencies** (1.5, 1.6) - Verify fail-fast behavior
2. **Invalid ABI Files** (2.5, 2.6) - Confirm startup validation
3. **Database Failures** (4.6, 4.7) - Test error handling
4. **Configuration Errors** (5.4, 5.5) - Validate configuration validation

### Recovery Tests
1. **WebSocket Failures** (3.6, 6.1) - Test auto-recovery
2. **Retry Mechanisms** (6.3) - Verify resilience patterns

---

## Test Environment Requirements

- **S3**: Bucket with valid ABI JSON files in zone directories
- **DynamoDB**: Events and BlockHeight tables accessible
- **Ethereum**: WebSocket endpoint for blockchain connection
- **Environment Variables**: All required configuration set
- **Network**: Connectivity to all external dependencies
