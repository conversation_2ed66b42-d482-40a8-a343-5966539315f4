simpleArgs: &simple-args
  accountId: "0x3630320000000000000000000000000000000000000000000000000000000000"
  fromZoneId: 3001
  toZoneId: 3000
  amount: 1

test:
  name: discharge
  description: >-
    custom discharge
  workers:
    type: local
    number: 1
  rounds:
    - label: discharge-round1
      description: Test description for discharge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/discharge/discharge.js
        arguments:
          << : *simple-args
    - label: discharge-round2
      description: Test description for discharge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/discharge/discharge.js
        arguments:
          << : *simple-args
    - label: discharge-round3
      description: Test description for discharge
      txNumber: 1
      rateControl:
        type: fixed-rate
        opts:
          tps: 1
      workload:
        module: ../benchmarks/scenario/discharge/discharge.js
        arguments:
          << : *simple-args
